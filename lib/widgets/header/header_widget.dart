import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'support_button.dart';
import 'more_menu.dart';
import '../../navigation/app_router.dart';

class HeaderWidget extends StatelessWidget {
  const HeaderWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: 80,
      decoration: const BoxDecoration(
        color: Color(0xFFA5D7C2),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                IconButton(
                  icon: Image.asset(
                    'assets/logo.png',
                    width: 120,
                    height: 70,
                    fit: BoxFit.contain,
                    errorBuilder: (context, error, stackTrace) {
                      print('Error loading image: $error');
                      return const Icon(Icons.error);
                    },
                  ),
                  onPressed: () {
                    final user = FirebaseAuth.instance.currentUser;
                    if (user != null) {
                      Navigator.pushReplacementNamed(context, AppRouter.homeRoute);
                    } else {
                      Navigator.pushReplacementNamed(context, AppRouter.loginRoute);
                    }
                  },
                ),
              ],
            ),
            Row(
              children: const [
                SupportButton(),
                SizedBox(width: 16),
                MoreMenu(),
              ],
            ),
          ],
        ),
      ),
    );
  }
} 