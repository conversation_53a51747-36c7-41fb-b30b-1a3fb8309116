import 'package:flutter/material.dart';
import '../widgets/header/header_widget.dart';
import 'home/components/navigation_icons.dart';
import '../navigation/app_router.dart';
import '../models/roadmap_preferences.dart';

class RoadmapsScreen extends StatelessWidget {
  final RoadmapPreferences? preferences;

  const RoadmapsScreen({
    super.key,
    this.preferences,
  });

  // Get total semesters from preferences or use default
  int get totalSemesters {
    if (preferences != null) {
      return preferences!.totalSemesters;
    }
    return 9; // Default to 9 semesters (4 years + 1 semester)
  }

  // Sample data for subjects - you can replace this with your actual data
  final Map<String, List<String>> semesterSubjects = const {
    '1-1': ['CS101', 'MATH101', 'PHYS101', 'ENG101', 'HUM101'],
    '1-2': ['CS102', 'MATH102', 'PHYS102', 'ENG102', 'HUM102'],
    '2-1': ['CS201', 'MATH201', 'PHYS201', 'ENG201', 'HUM201'],
    '2-2': ['CS202', 'MATH202', 'PHYS202', 'ENG202', 'HUM202'],
    '3-1': ['CS301', 'MATH301', 'PHYS301', 'ENG301', 'HUM301'],
    '3-2': ['CS302', 'MATH302', 'PHYS302', 'ENG302', 'HUM302'],
    '4-1': ['CS401', 'MATH401', 'PHYS401', 'ENG401', 'HUM401'],
    '4-2': ['CS402', 'MATH402', 'PHYS402', 'ENG402', 'HUM402'],
    '5-1': ['CS501', 'MATH501', 'PHYS501', 'ENG501', 'HUM501'],
    '5-2': ['CS502', 'MATH502', 'PHYS502', 'ENG502', 'HUM502'],
  };

  @override
  Widget build(BuildContext context) {

    
    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          const HeaderWidget(),
          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Academic Roadmaps',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                    const SizedBox(height: 10),
                    if (preferences != null) ...[
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: const Color(0xFFA5D7C2).withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: const Color(0xFF2E7D32), width: 1),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Roadmap Configuration',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF2E7D32),
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text('Department: ${preferences!.selectedDepartment ?? 'Not specified'}'),
                            Text('Target Graduation: ${preferences!.targetGraduationYear}'),
                            Text('Max Subjects per Semester: ${preferences!.maxSubjectsPerSemester}'),
                            Text('Include Summer Semesters: ${preferences!.includeSummerSemester ? 'Yes' : 'No'}'),
                            Text('Total Semesters: $totalSemesters'),
                          ],
                        ),
                      ),
                      const SizedBox(height: 20),
                    ],
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              Navigator.pushNamed(context, AppRouter.semesterInfoRoute);
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF2E7D32),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 15),
                            ),
                            child: const Text(
                              'Enter Semester Info',
                              style: TextStyle(fontSize: 16),
                            ),
                          ),
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              Navigator.pushNamed(context, AppRouter.roadmapCustomizationRoute);
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF2E7D32),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 15),
                            ),
                            child: const Text(
                              'Customize Roadmap',
                              style: TextStyle(fontSize: 16),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    _buildSemesterLayout(context),
                  ],
                ),
              ),
            ),
          ),
          Container(
            width: MediaQuery.of(context).size.width,
            height: 80,
            decoration: const BoxDecoration(
              color: Color(0xFFA5D7C2),
            ),
            child: const NavigationIcons(),
          ),
        ],
      ),
    );
  }

  Widget _buildSemesterLayout(BuildContext context) {
    if (preferences == null) {
      // Default layout for when no preferences are provided
      final defaultYears = 4;

      return ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: defaultYears,
        itemBuilder: (context, yearIndex) {
          final year = yearIndex + 1;
          return _buildYearSection(context, year, hasSecondSemester: true, hasSummerSemester: false);
        },
      );
    }

    // Calculate years based on preferences
    final currentYear = DateTime.now().year;
    final yearsToGraduation = preferences!.targetGraduationYear - currentYear;
    final includeSummer = preferences!.includeSummerSemester;

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: yearsToGraduation,
      itemBuilder: (context, yearIndex) {
        final year = yearIndex + 1;
        final isLastYear = year == yearsToGraduation;

        // For the last year, check if we need both semesters
        final hasSecondSemester = !isLastYear || (totalSemesters % 2 == 0);

        return _buildYearSection(
          context,
          year,
          hasSecondSemester: hasSecondSemester,
          hasSummerSemester: includeSummer && !isLastYear,
        );
      },
    );
  }

  Widget _buildYearSection(BuildContext context, int year, {required bool hasSecondSemester, required bool hasSummerSemester}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 10.0),
          child: Text(
            'Year $year',
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2E7D32),
            ),
          ),
        ),
        // Fall and Spring semesters
        Row(
          children: [
            Expanded(
              child: _buildRoadmapCard(
                context,
                year,
                'Fall',
                semesterSubjects['$year-1'] ?? ['No subjects assigned'],
              ),
            ),
            if (hasSecondSemester) ...[
              const SizedBox(width: 15),
              Expanded(
                child: _buildRoadmapCard(
                  context,
                  year,
                  'Spring',
                  semesterSubjects['$year-2'] ?? ['No subjects assigned'],
                ),
              ),
            ],
          ],
        ),
        // Summer semester if included
        if (hasSummerSemester) ...[
          const SizedBox(height: 15),
          Row(
            children: [
              Expanded(
                flex: 1,
                child: Container(), // Empty space for alignment
              ),
              Expanded(
                flex: 2,
                child: _buildRoadmapCard(
                  context,
                  year,
                  'Summer',
                  semesterSubjects['$year-summer'] ?? ['Summer courses'],
                ),
              ),
              Expanded(
                flex: 1,
                child: Container(), // Empty space for alignment
              ),
            ],
          ),
        ],
        const SizedBox(height: 20),
      ],
    );
  }

  Widget _buildRoadmapCard(
    BuildContext context,
    int year,
    String semester,
    List<String> subjects,
  ) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15),
      ),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15),
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFFA5D7C2), Color(0xFF81C784)],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '$semester Semester',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 12),
            const Divider(color: Colors.white30),
            const SizedBox(height: 8),
            ...subjects.map((subject) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4.0),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          subject,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                )),
          ],
        ),
      ),
    );
  }
} 