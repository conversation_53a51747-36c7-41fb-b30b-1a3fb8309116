import 'package:flutter/material.dart';
import 'calendar_screen.dart';

class InputMenuPage extends StatefulWidget {
  const InputMenuPage({super.key});

  @override
  State<InputMenuPage> createState() => _InputMenuPageState();
}

class _InputMenuPageState extends State<InputMenuPage> {
  final List<String> _departments = ['Computer Engineering'];
  final List<String> _semesters = ['Semester 4', 'Semester 6', 'Semester 8', 'semester 10'];
  final Map<String, bool> _excludedDays = {
    'Sun': false,
    'Mon': false,
    'Tue': false,
    'Wed': false,
    'Thu': false,
    'Sat': false,
  };

  final Map<String, List<String>> _semesterCourses = {
    'Semester 4': [
      'Digital Logic Circuits I',
      'Analysis of Electrical Circuits',
      'Data Structures I',
      'Probability & Statistics',
      'Differential Equations',
    ],
    'Semester 6': [
      'Computer Organization',
      'Data Structures II',
      'Microprocessor Systems',
      'System Programming',
      'Digital Signal Processing',
      'Control Systems',
    ],
    'Semester 8': [
      'Embedded Systems',
      'Digital Communications',
      'Computer Vision',
      'Machine Learning',
    ],
    'Semester 10': [
      'Computer and Network Security',
      'Natural Language Processing',
      'Distributed Systems',
      'Internet of Things',
      'Cloud Computing',
      'Senior project 2'
    ],
  };


  String? _selectedDepartment;
  String? _selectedSemester;
  String? _selectedCourse;
  List<String> _selectedCourses = [];
  List<String> _availableCourses = [];

  double _densityValue = 1; // Default is Medium
  final List<String> _densityLabels = ['Low', 'Medium', 'High'];
  double _timePreference = 1;

  // Schedule generation data
  final List<String> _days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
  final List<String> _timeSlots = [
    '8:30 - 10:10',
    '10:20 - 12:00',
    '12:10 - 1:50',
    '2:00 - 3:40',
    '3:50 - 5:30',
    '5:40 - 7:20'
  ];

  // Sample default courses with schedule
  // Full list of all courses (lectures, labs, tutorials/sections) for 8th & 10th
  final List<Map<String, dynamic>> _allCourses = [
    // ——————— 8th Term ———————
    {
      'name': 'Embedded Systems',
      'type': 'lecture',
      'day': 'Sat',
      'timeSlotIndex': 0,
      'duration': 1,
    },
    {
      'name': 'Embedded Systems - Lab',
      'type': 'lab',
      'parent': 'Embedded Systems',
      'day': 'Sat',
      'timeSlotIndex': 2,
      'duration': 1.0,
    },
    {
      'name': 'Digital Communications',
      'type': 'lecture',
      'day': 'Sat',
      'timeSlotIndex': 0,
      'duration': 1,
    },
    {
      'name': 'Digital Communications - Tutorial',
      'type': 'tut',
      'parent': 'Digital Communications',
      'day': 'Sun',
      'timeSlotIndex': 0,
      'duration': 1.0,
    },
    {
      'name': 'Computer Vision',
      'type': 'lecture',
      'day': 'Tue',
      'timeSlotIndex': 4,
      'duration': 1.5,
    },
    {
      'name': 'Computer Vision - Lab',
      'type': 'lab',
      'parent': 'Computer Vision',
      'day': 'Wed',
      'timeSlotIndex': 3,
      'duration': 1.0,
    },
    {
      'name': 'Optimization techniques',
      'type': 'lecture',
      'day': 'Sun',
      'timeSlotIndex': 4,
      'duration': 1.5,
    },
    {
      'name': ' Optimization techniques - tutorial',
      'type': 'tut',
      'parent': 'Optimization techniques',
      'day': 'Sun',
      'timeSlotIndex': 5,
      'duration': 1.0,
    },
    {
      'name': 'Machine Learning',
      'type': 'lecture',
      'day': 'Wed',
      'timeSlotIndex': 0,
      'duration': 1,
    },
    {
      'name': 'Machine Learning - Tutorial',
      'type': 'tut',
      'parent': 'Machine Learning',
      'day': 'Tue',
      'timeSlotIndex': 3,
      'duration': 1.0,
    },
    {
      'name': 'Mobile and wireless communication',
      'type': 'lecture',
      'day': 'Mon',
      'timeSlotIndex': 1,
      'duration': 1,
    },
    {
      'name': 'Mobile and wireless communication - tutorial',
      'type': 'tut',
      'parent': 'Mobile and wireless communication',
      'day': 'Mon',
      'timeSlotIndex': 0,
      'duration': 1.0,
    },
    {
      'name': 'Mobile and wireless communication',
      'type': 'Lab',
      'parent': 'Mobile and wireless communication',
      'day': 'Mon',
      'timeSlotIndex': 2,
      'duration': 1.0,
    },

    // ——————— 10th Term ———————
    {
      'name': 'Computer and Network Security',
      'type': 'lecture',
      'day': 'Sat',
      'timeSlotIndex': 1,
      'duration': 1,
    },
    {
      'name': 'Computer and Network Security - Tutorial',
      'type': 'tut',
      'parent': 'Computer and Network Security',
      'day': 'Sat',
      'timeSlotIndex': 0,
      'duration': 1.0,
    },
    {
      'name': 'Senior project 2',
      'type': 'lecture',
      'day': 'Sat',
      'timeSlotIndex': 6,
      'duration': 1,
    },
    {
      'name': 'Data Mining ',
      'type': 'lecture',
      'day': 'Sun',
      'timeSlotIndex': 0,
      'duration': 1,
    },
    {
      'name': 'Data Mining - Lab',
      'type': 'lab',
      'parent': 'Data Mining',
      'day': 'Sun',
      'timeSlotIndex': 2,
      'duration': 1.0,
    },
    {
      'name': 'Sensors and Intelligent System - Lab',
      'type': 'lab',
      'parent': 'Sensors and Intelligent System',
      'day': 'Sun',
      'timeSlotIndex': 2,
      'duration': 1.0,
    },
    {
      'name': 'Sensors and Intelligent System - Tut',
      'type': 'tut',
      'parent': 'Sensors and Intelligent System',
      'day': 'Sun',
      'timeSlotIndex': 3,
      'duration': 1.0,
    },
    {
      'name': 'Sensors and Intelligent System - Lec',
      'type': 'lec',
      'parent': 'Sensors and Intelligent System',
      'day': 'Mon',
      'timeSlotIndex': 1,
      'duration': 1.0,
    },
    {
      'name': 'Artificial Intelligence - Tutorial',
      'type': 'tut',
      'parent': 'Artificial Intelligence',
      'day': 'Mon',
      'timeSlotIndex': 2,
      'duration': 1.0,
    },
    {
      'name': 'Artificial Intelligence - Lec',
      'type': 'tut',
      'parent': 'Artificial Intelligence',
      'day': 'Mon',
      'timeSlotIndex': 3,
      'duration': 1.0,
    },
    {
      'name': 'project 2 - Lab',
      'type': 'lab',
      'parent': 'project 2',
      'day': 'Mon',
      'timeSlotIndex': 6,
      'duration': 1.0,
    },
    {
      'name': 'Artificial Intelligence - Lec',
      'type': 'tut',
      'parent': 'Artificial Intelligence',
      'day': 'Tue',
      'timeSlotIndex': 3,
      'duration': 1.0,
    },
    {
      'name': 'Net-Centric Computing - Lec',
      'type': 'lec',
      'parent': 'Net-Centric Computing',
      'day': 'Tue',
      'timeSlotIndex': 2,
      'duration': 1.0,
    },
    {
      'name': 'Computer Vision - Lab',
      'type': 'lab',
      'parent': 'Computer Vision',
      'day': 'Tue',
      'timeSlotIndex': 3,
      'duration': 1.0,
    },
    {
      'name': 'Computer Vision - Lec',
      'type': 'lec',
      'parent': 'Computer Vision',
      'day': 'Tue',
      'timeSlotIndex': 4,
      'duration': 1.0,
    },
    {
      'name': 'Computer Vision - Tut',
      'type': 'tut',
      'parent': 'Computer Vision',
      'day': 'Tue',
      'timeSlotIndex': 5,
      'duration': 1.0,
    },
    {
      'name': 'New trends in CCE - Lec',
      'type': 'lec',
      'parent': 'New trends in CCE',
      'day': 'Wed',
      'timeSlotIndex': 1,
      'duration': 1.0,
    },
    {
      'name': 'New trends in CCE - Tut',
      'type': 'tut',
      'parent': 'New trends in CCE',
      'day': 'Wed',
      'timeSlotIndex': 2,
      'duration': 1.0,
    },
    {
      'name': 'Software Engineering - Tut',
      'type': 'tut',
      'parent': 'Software Engineering',
      'day': 'Wed',
      'timeSlotIndex': 3,
      'duration': 1.0,
    },
    {
      'name': 'Net-Centric Computing - Tut',
      'type': 'tut',
      'parent': 'Net-Centric Computing',
      'day': 'Wed',
      'timeSlotIndex': 3,
      'duration': 1.0,
    },
    {
      'name': 'Software Engineering - Tut',
      'type': 'tut',
      'parent': 'Software Engineering',
      'day': 'Wed',
      'timeSlotIndex': 4,
      'duration': 1.0,
    },
    // Semester 4 Courses
    ...[
      {'name': 'Digital Logic Circuits I', 'type': 'lecture', 'day': 'Sat', 'timeSlotIndex': 3, 'duration': 1.5},
      {'name': 'Digital Logic Circuits I - Lab', 'type': 'lab', 'parent': 'Digital Logic Circuits I', 'day': 'Wed', 'timeSlotIndex': 4, 'duration': 1.0},
      {'name': 'Digital Logic Circuits I - Tut', 'type': 'tut', 'parent': 'Digital Logic Circuits I', 'day': 'Mon', 'timeSlotIndex': 2, 'duration': 1.0},

      {'name': 'Analysis of Electrical Circuits', 'type': 'lecture', 'day': 'Tue', 'timeSlotIndex': 1, 'duration': 1.5},
      {'name': 'Analysis of Electrical Circuits - Lab', 'type': 'lab', 'parent': 'Analysis of Electrical Circuits', 'day': 'Sun', 'timeSlotIndex': 1, 'duration': 1.0},
      {'name': 'Analysis of Electrical Circuits - Tut', 'type': 'tut', 'parent': 'Analysis of Electrical Circuits', 'day': 'Mon', 'timeSlotIndex': 5, 'duration': 1.0},

      {'name': 'Data Structures I', 'type': 'lecture', 'day': 'Sun', 'timeSlotIndex': 0, 'duration': 1.5},
      {'name': 'Data Structures I - Lab', 'type': 'lab', 'parent': 'Data Structures I', 'day': 'Wed', 'timeSlotIndex': 4, 'duration': 1.0},
      {'name': 'Data Structures I - Tut', 'type': 'tut', 'parent': 'Data Structures I', 'day': 'Tue', 'timeSlotIndex': 3, 'duration': 1.0},

      {'name': 'Probability & Statistics', 'type': 'lecture', 'day': 'Thu', 'timeSlotIndex': 2, 'duration': 1.5},
      {'name': 'Probability & Statistics - Tut', 'type': 'tut', 'parent': 'Probability & Statistics', 'day': 'Wed', 'timeSlotIndex': 0, 'duration': 1.0},

      {'name': 'Differential Equations', 'type': 'lecture', 'day': 'Tue', 'timeSlotIndex': 0, 'duration': 1.5},
      {'name': 'Differential Equations - Tut', 'type': 'tut', 'parent': 'Differential Equations', 'day': 'Thu', 'timeSlotIndex': 5, 'duration': 1.0},
    ],

    // Semester 6 Courses
    ...[
      {'name': 'Computer Organization', 'type': 'lecture', 'day': 'Wed', 'timeSlotIndex': 0, 'duration': 1.5},
      {'name': 'Computer Organization - Lab', 'type': 'lab', 'parent': 'Computer Organization', 'day': 'Mon', 'timeSlotIndex': 3, 'duration': 1.0},
      {'name': 'Computer Organization - Tut', 'type': 'tut', 'parent': 'Computer Organization', 'day': 'Thu', 'timeSlotIndex': 3, 'duration': 1.0},

      {'name': 'Data Structures II', 'type': 'lecture', 'day': 'Wed', 'timeSlotIndex': 2, 'duration': 1.5},
      {'name': 'Data Structures II - Lab', 'type': 'lab', 'parent': 'Data Structures II', 'day': 'Sat', 'timeSlotIndex': 1, 'duration': 1.0},
      {'name': 'Data Structures II - Tut', 'type': 'tut', 'parent': 'Data Structures II', 'day': 'Mon', 'timeSlotIndex': 1, 'duration': 1.0},

      {'name': 'Microprocessor Systems', 'type': 'lecture', 'day': 'Tue', 'timeSlotIndex': 2, 'duration': 1.5},
      {'name': 'Microprocessor Systems - Lab', 'type': 'lab', 'parent': 'Microprocessor Systems', 'day': 'Wed', 'timeSlotIndex': 4, 'duration': 1.0},
      {'name': 'Microprocessor Systems - Tut', 'type': 'tut', 'parent': 'Microprocessor Systems', 'day': 'Sun', 'timeSlotIndex': 5, 'duration': 1.0},

      {'name': 'System Programming', 'type': 'lecture', 'day': 'Thu', 'timeSlotIndex': 2, 'duration': 1.5},
      {'name': 'System Programming - Tut', 'type': 'tut', 'parent': 'System Programming', 'day': 'Sun', 'timeSlotIndex': 3, 'duration': 1.0},

      {'name': 'Digital Signal Processing', 'type': 'lecture', 'day': 'Tue', 'timeSlotIndex': 0, 'duration': 1.5},
      {'name': 'Digital Signal Processing - Lab', 'type': 'lab', 'parent': 'Digital Signal Processing', 'day': 'Wed', 'timeSlotIndex': 4, 'duration': 1.0},
      {'name': 'Digital Signal Processing - Tut', 'type': 'tut', 'parent': 'Digital Signal Processing', 'day': 'Thu', 'timeSlotIndex': 3, 'duration': 1.0},

      {'name': 'Control Systems', 'type': 'lecture', 'day': 'Thu', 'timeSlotIndex': 0, 'duration': 1.5},
      {'name': 'Control Systems - Tut', 'type': 'tut', 'parent': 'Control Systems', 'day': 'Thu', 'timeSlotIndex': 4, 'duration': 1.0},
    ],

  ];


  List<List<Map<String, dynamic>>> _generateRankedSchedules(List<String> selectedCourses, List<String> excludedDays) {
    // 1. Match selected courses (lectures)
    final mainCourses = _allCourses.where((c) =>
    selectedCourses.contains(c['name']) && c['type'] == 'lecture').toList();

    // 2. Group lecture instances by course name
    final Map<String, List<Map<String, dynamic>>> groupedLectures = {};
    for (var c in mainCourses) {
      groupedLectures.putIfAbsent(c['name'], () => []);
      groupedLectures[c['name']]!.add(c);
    }

    // 3. Generate all combinations of lecture instances
    List<List<Map<String, dynamic>>> combinations = [[]];
    for (var options in groupedLectures.values) {
      combinations = [
        for (var combo in combinations)
          for (var c in options) [...combo, c]
      ];
    }

    // 4. Attach labs/sections for each combination
    for (int i = 0; i < combinations.length; i++) {
      final extended = <Map<String, dynamic>>[];
      for (var course in combinations[i]) {
        extended.add(course);
        final related = _allCourses.where((c) =>
        (c['type'] == 'lab' || c['type'] == 'section') &&
            c['parent'] == course['name']).toList();
        extended.addAll(related);
      }
      combinations[i] = extended;
    }

    // 5. Remove time-conflicting combos
    combinations = combinations.where((combo) {
      final seen = <String, int>{};
      for (var c in combo) {
        final key = '${c['day']}-${c['timeSlotIndex']}';
        if (seen.containsKey(key)) return false;
        seen[key] = 1;
      }
      return true;
    }).toList();

    // 6. Excluded days
    combinations = combinations.where((combo) {
      return !combo.any((c) => excludedDays.contains(c['day']));
    }).toList();

    // 7. Density (avg courses per day)
    combinations.sort((a, b) {
      double avgA = a.length / a.map((c) => c['day']).toSet().length;
      double avgB = b.length / b.map((c) => c['day']).toSet().length;
      return _densityValue > 0.5 ? avgB.compareTo(avgA) : avgA.compareTo(avgB);
    });
    combinations = combinations.take(10).toList();

    // 8. Time preference
    combinations.sort((a, b) {
      int earlyScore(List<Map<String, dynamic>> combo) =>
          combo.where((c) => c['timeSlotIndex'] <= 2).length;

      return _timePreference > 0.5
          ? earlyScore(b).compareTo(earlyScore(a))
          : earlyScore(a).compareTo(earlyScore(b));
    });

    return combinations.take(10).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: const Text('Input Menu'),
          backgroundColor: const Color(0xFFA5D7C2),
        ),
        body: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Department Dropdown
                DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                      labelText: 'Select Department',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(40.0),
                      )
                  ),
                  value: _selectedDepartment,
                  items: _departments.map((dept) {
                    return DropdownMenuItem<String>(
                      value: dept,
                      child: Text(dept),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedDepartment = value;
                    });
                  },
                ),
                const SizedBox(height: 20),

                // Semester Dropdown
                DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                      labelText: 'Select Semester',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(40.0),
                      )
                  ),
                  value: _selectedSemester,
                  items: _semesters.map((sem) {
                    return DropdownMenuItem<String>(
                      value: sem,
                      child: Text(sem),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedSemester = value;
                      _availableCourses = _semesterCourses[value] ?? [];
                      _selectedCourse = null;
                    });
                  },
                ),
                const SizedBox(height: 20),

                // Course Dropdown
                DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    labelText: 'Select course',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(40.0),
                    ),
                  ),
                  value: _selectedCourse,
                  items: _availableCourses.map((course) {
                    return DropdownMenuItem<String>(
                      value: course,
                      child: Text(course),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedCourse = value;
                      if (value != null && !_selectedCourses.contains(value)) {
                        _selectedCourses.add(value);
                      }
                    });
                  },
                ),
                const SizedBox(height: 30),

                // Course Density Slider
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Course Density',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    Slider(
                      value: _densityValue,
                      min: 0,
                      max: 2,
                      divisions: 2,
                      label: _densityLabels[_densityValue.toInt()],
                      activeColor: const Color(0xFF104B63),
                      onChanged: (value) {
                        setState(() {
                          _densityValue = value;
                        });
                      },
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: const [
                        Text('Low'),
                        Text('Medium'),
                        Text('High'),
                      ],
                    ),
                  ],
                ),

                const SizedBox(height: 30),

                // Time Preference Slider
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Time Preference',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    Slider(
                      value: _timePreference,
                      min: 0,
                      max: 1,
                      divisions: 1,
                      label: _timePreference == 0 ? 'Early' : 'Late',
                      activeColor: const Color(0xFF104B63),
                      onChanged: (value) {
                        setState(() {
                          _timePreference = value;
                        });
                      },
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: const [
                        Text('Early'),
                        Text('Late'),
                      ],
                    ),
                  ],
                ),

                const SizedBox(height: 30),
                const Text(
                  'Selected Courses',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 10),
                Wrap(
                  spacing: 8.0,
                  runSpacing: 8.0,
                  children: _selectedCourses.map((course) {
                    return Chip(
                      label: Text(course),
                      deleteIcon: const Icon(Icons.close),
                      onDeleted: () {
                        setState(() {
                          _selectedCourses.remove(course);
                        });
                      },
                      backgroundColor: const Color(0xFFA5D7C2),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                        side: const BorderSide(color: Color(0xFF104B63)),
                      ),
                    );
                  }).toList(),
                ),

                const SizedBox(height: 30),
                const Text(
                  'Exclude Days',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 10),
                Wrap(
                  spacing: 10.0,
                  runSpacing: 5.0,
                  children: _excludedDays.keys.map((day) {
                    return FilterChip(
                      label: Text(day),
                      selected: _excludedDays[day]!,
                      onSelected: (selected) {
                        setState(() {
                          _excludedDays[day] = selected;
                        });
                      },
                      selectedColor: const Color(0xFF104B63),
                      backgroundColor: const Color(0xFFE0F7FA),
                      checkmarkColor: Colors.white,
                    );
                  }).toList(),
                ),

                const SizedBox(height: 30),
                Center(
                  child: ElevatedButton(
                    onPressed: () {
                      if (_selectedCourses.isEmpty) {
                        showDialog(
                          context: context,
                          builder: (context) => AlertDialog(
                            title: const Text("No Courses Selected"),
                            content: const Text("Please select at least one course before scheduling."),
                            actions: [
                              TextButton(
                                onPressed: () => Navigator.of(context).pop(),
                                child: const Text("OK"),
                              ),
                            ],
                          ),
                        );
                      } else {
                        final excludedDays = _excludedDays.entries
                            .where((entry) => entry.value)
                            .map((entry) => entry.key)
                            .toList();

                        final topSchedules = _generateRankedSchedules(_selectedCourses, excludedDays);

                        // Check if any schedule contains all selected courses
                        bool hasCompleteSchedule = false;
                        for (var schedule in topSchedules) {
                          final scheduledCourseNames = schedule
                              .where((c) => c['type'] == 'lecture')
                              .map((c) => c['name'])
                              .toSet();

                          if (scheduledCourseNames.containsAll(_selectedCourses)) {
                            hasCompleteSchedule = true;
                            break;
                          }
                        }

                        if (!hasCompleteSchedule) {
                          showDialog(
                            context: context,
                            builder: (context) => AlertDialog(
                              title: const Text("Schedule Conflict"),
                              content: const Text("Too many conflicts, can't calculate complete schedule with all selected courses."),
                              actions: [
                                TextButton(
                                  onPressed: () => Navigator.of(context).pop(),
                                  child: const Text("OK"),
                                ),
                              ],
                            ),
                          );
                        } else {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => CalendarScreen(
                                topSchedules: topSchedules,
                                days: _days,
                                timeSlots: _timeSlots,
                              ),
                            ),
                          );
                        }
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF104B63),
                      padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 15),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30),
                      ),
                    ),
                    child: const Text(
                      'Schedule',
                      style: TextStyle(color: Colors.white, fontSize: 16),
                    ),
                  ),
                )
              ],
            ),
          ),
        )
    );
  }
}