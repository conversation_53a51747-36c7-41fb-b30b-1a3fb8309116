import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../widgets/header/header_widget.dart';
import 'home/components/navigation_icons.dart';
import 'professors/components/professor_card.dart';
import 'professors/components/filter_menu.dart';
import '../utils/firebase_api.dart';

class ProfessorsScreen extends StatefulWidget {
  const ProfessorsScreen({super.key});

  @override
  State<ProfessorsScreen> createState() => _ProfessorsScreenState();
}

class _ProfessorsScreenState extends State<ProfessorsScreen> {
  String? selectedDepartment;
  List<Map<String, dynamic>> professors = [];
  List<String> departments = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadProfessors();
  }

  Future<void> _loadProfessors() async {
    try {
      final firebaseApi = Get.put(FirebaseApi());
      final doctorsData = await firebaseApi.getAllDoctors();

      setState(() {
        professors = doctorsData;
        // Extract unique departments from the professors data
        departments = doctorsData
            .map((prof) => prof['department'] as String)
            .where((dept) => dept.isNotEmpty)
            .toSet()
            .toList();
        departments.sort(); // Sort alphabetically
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading professors: $e');
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to load professors. Please check your internet connection.';
      });
    }
  }

  List<Map<String, dynamic>> get filteredProfessors {
    if (selectedDepartment == null || selectedDepartment == 'all') {
      return professors;
    }
    return professors.where((prof) => prof['department'] == selectedDepartment).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          const HeaderWidget(),
          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: Column(
                  children: [
                    const SizedBox(height: 20),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        _isLoading
                            ? const SizedBox(
                                width: 100,
                                height: 40,
                                child: Center(
                                  child: SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(strokeWidth: 2),
                                  ),
                                ),
                              )
                            : FilterMenu(
                                departments: departments,
                                onFilterChanged: (department) {
                                  setState(() {
                                    selectedDepartment = department;
                                  });
                                },
                              ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    if (_isLoading)
                      const Center(
                        child: Padding(
                          padding: EdgeInsets.all(50.0),
                          child: CircularProgressIndicator(),
                        ),
                      )
                    else if (_errorMessage != null)
                      Center(
                        child: Padding(
                          padding: const EdgeInsets.all(50.0),
                          child: Column(
                            children: [
                              const Icon(
                                Icons.error_outline,
                                size: 48,
                                color: Colors.red,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                _errorMessage!,
                                style: const TextStyle(
                                  fontSize: 16,
                                  color: Colors.red,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 16),
                              ElevatedButton(
                                onPressed: () {
                                  setState(() {
                                    _isLoading = true;
                                    _errorMessage = null;
                                  });
                                  _loadProfessors();
                                },
                                child: const Text('Retry'),
                              ),
                            ],
                          ),
                        ),
                      )
                    else if (filteredProfessors.isEmpty)
                      const Center(
                        child: Padding(
                          padding: EdgeInsets.all(50.0),
                          child: Text(
                            'No professors found',
                            style: TextStyle(
                              fontSize: 18,
                              color: Colors.grey,
                            ),
                          ),
                        ),
                      )
                    else
                      ...filteredProfessors.map((professor) => Padding(
                            padding: const EdgeInsets.only(bottom: 16.0),
                            child: ProfessorCard(
                              name: professor['name'] ?? 'Unknown Name',
                              department: professor['department'] ?? 'Unknown Department',
                              email: professor['email'] ?? 'No email provided',
                              office: professor['office'] ?? 'Office not specified',
                              imageUrl: professor['imageUrl'] ?? 'https://via.placeholder.com/150',
                            ),
                          )),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ),
          Container(
            width: MediaQuery.of(context).size.width,
            height: 80,
            decoration: const BoxDecoration(
              color: Color(0xFFA5D7C2),
            ),
            child: const NavigationIcons(),
          ),
        ],
      ),
    );
  }
} 