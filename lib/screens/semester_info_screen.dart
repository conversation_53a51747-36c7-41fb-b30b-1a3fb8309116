import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import 'package:secndversion_of_gradproj/utils/firebase_api.dart';

class SemesterInfoScreen extends StatefulWidget {
  const SemesterInfoScreen({super.key});

  @override
  State<SemesterInfoScreen> createState() => _SemesterInfoScreenState();
}

class _SemesterInfoScreenState extends State<SemesterInfoScreen> {
  final _formKey = GlobalKey<FormState>();
  int _currentSemester = 1;
  final List<String> _completedSubjects = [];
  String? _selectedSubject;
  List<Map<String, dynamic>> _allSubjects = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSubjects();
  }

  Future<void> _loadSubjects() async {
    try {
      setState(() {
        _isLoading = true;
      });

      print('Starting to load subjects...');
      
      // Check if user is authenticated
      final user = FirebaseAuth.instance.currentUser;
      print('Current user: ${user?.uid ?? 'Not authenticated'}');
      final firebaseapi = Get.put(FirebaseApi());

      final List<Map<String, dynamic>> subjects =  await firebaseapi.getAllSubjects();

      setState(() {
        _allSubjects = subjects;
        _isLoading = false;
      });
      
      print('Successfully loaded ${subjects.length} subjects');
    } catch (e) {
      print('Error loading subjects: $e');
      print('Error type: ${e.runtimeType}');
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading subjects: $e'),
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  List<Map<String, dynamic>> get availableSubjects {
    return _allSubjects
        .where((subject) => 
            subject['semester_number'] == _currentSemester &&
            !_completedSubjects.contains(subject['code']))
        .toList();
  }

  void _addSubject() {
    if (_selectedSubject != null && !_completedSubjects.contains(_selectedSubject!)) {
      setState(() {
        _completedSubjects.add(_selectedSubject!);
        _selectedSubject = null; // Reset selection
      });
    }
  }

  void _removeSubject(int index) {
    setState(() {
      _completedSubjects.removeAt(index);
    });
  }

  String _getSubjectDisplayName(Map<String, dynamic> subject) {
    // Display format: "CODE - English Name"
    return '${subject['code']} - ${subject['englishName']}';
  }

  String _getCompletedSubjectDisplayName(String code) {
    final subject = _allSubjects.firstWhere(
      (s) => s['code'] == code,
      orElse: () => {'code': code, 'englishName': 'Unknown Subject'},
    );
    return '${subject['code']} - ${subject['englishName']}';
  }

  Future<void> _saveSemesterInfo() async {
    if (_formKey.currentState!.validate()) {
      try {
        final user = FirebaseAuth.instance.currentUser;

        if (user != null) {
          await FirebaseFirestore.instance
              .collection('users')
              .doc(user.uid)
              .collection('academic_info')
              .doc('semester_info')
              .set({
            'current_semester': _currentSemester,
            'completed_subjects': _completedSubjects,
            'updated_at': FieldValue.serverTimestamp(),
          });

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Semester information saved successfully!')),
            );
            Navigator.pop(context);
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error saving semester information: $e')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Semester Information'),
        backgroundColor: const Color(0xFFA5D7C2),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadSubjects,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Loading subjects...'),
                ],
              ),
            )
          : Padding(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Current Semester Dropdown
                    DropdownButtonFormField<int>(
                      value: _currentSemester,
                      decoration: const InputDecoration(
                        labelText: 'Current Semester',
                        border: OutlineInputBorder(),
                      ),
                      items: List.generate(10, (index) => index + 1)
                          .map((semester) => DropdownMenuItem(
                                value: semester,
                                child: Text('Semester $semester'),
                              ))
                          .toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _currentSemester = value;
                            _selectedSubject = null; // Reset subject selection
                          });
                        }
                      },
                    ),

                    const SizedBox(height: 20),

                    // Add Subject Section
                    const Text(
                      'Add Completed Subject',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 10),

                    Row(
                      children: [
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _selectedSubject,
                            decoration: const InputDecoration(
                              labelText: 'Select Subject',
                              border: OutlineInputBorder(),
                            ),
                            hint: const Text('Choose a subject'),
                            items: availableSubjects.map((subject) {
                              return DropdownMenuItem<String>(
                                value: subject['code'],
                                child: Text(
                                  _getSubjectDisplayName(subject),
                                  style: const TextStyle(fontSize: 14),
                                ),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedSubject = value;
                              });
                            },
                            isExpanded: true,
                          ),
                        ),
                        const SizedBox(width: 10),
                        ElevatedButton(
                          onPressed: _selectedSubject != null ? _addSubject : null,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF2E7D32),
                            padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
                          ),
                          child: const Text(
                            'Add',
                            style: TextStyle(color: Colors.white),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 20),

                    // Completed Subjects List
                    Text(
                      'Completed Subjects (${_completedSubjects.length})',
                      style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 10),

                    Expanded(
                      child: _completedSubjects.isEmpty
                          ? const Center(
                              child: Text(
                                'No completed subjects added yet',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey,
                                ),
                              ),
                            )
                          : ListView.builder(
                              itemCount: _completedSubjects.length,
                              itemBuilder: (context, index) {
                                final subjectCode = _completedSubjects[index];
                                final subject = _allSubjects.firstWhere(
                                  (s) => s['code'] == subjectCode,
                                  orElse: () => {
                                    'code': subjectCode,
                                    'englishName': 'Unknown Subject',
                                    'hours': 0,
                                    'type': 'unknown'
                                  },
                                );

                                return Card(
                                  margin: const EdgeInsets.only(bottom: 8),
                                  child: ListTile(
                                    title: Text(
                                      _getCompletedSubjectDisplayName(subjectCode),
                                      style: const TextStyle(fontSize: 16),
                                    ),
                                    subtitle: Text(
                                      '${subject['hours']} hours • ${subject['type']}',
                                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                                    ),
                                    trailing: IconButton(
                                      icon: const Icon(Icons.delete, color: Colors.red),
                                      onPressed: () => _removeSubject(index),
                                    ),
                                  ),
                                );
                              },
                            ),
                    ),

                    const SizedBox(height: 20),

                    // Save Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _saveSemesterInfo,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF2E7D32),
                          padding: const EdgeInsets.symmetric(vertical: 15),
                        ),
                        child: const Text(
                          'Save Information',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}