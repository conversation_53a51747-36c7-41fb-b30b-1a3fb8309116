import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class RoadmapCustomizationScreen extends StatefulWidget {
  const RoadmapCustomizationScreen({super.key});

  @override
  State<RoadmapCustomizationScreen> createState() => _RoadmapCustomizationScreenState();
}

class _RoadmapCustomizationScreenState extends State<RoadmapCustomizationScreen> {
  final _formKey = GlobalKey<FormState>();
  int _targetGraduationYear = DateTime.now().year + 4;
  int _maxSubjectsPerSemester = 6;
  bool _includeSummerSemester = true;
  String _random_department = "cce";
  List<Map<String, dynamic>> _allDepartments = [];

  @override
  void initState() {
    super.initState();
    _loadExistingPreferences();
  }

  Future<void> _loadExistingPreferences() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        final doc = await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .collection('academic_info')
            .doc('roadmap_preferences')
            .get();

        if (doc.exists) {
          setState(() {
            _targetGraduationYear = doc.data()?['target_graduation_year'] ?? _targetGraduationYear;
            _maxSubjectsPerSemester = doc.data()?['max_subjects_per_semester'] ?? _maxSubjectsPerSemester;
            _includeSummerSemester = doc.data()?['include_summer_semester'] ?? _includeSummerSemester;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading preferences: $e')),
        );
      }
    }
  }

  Future<void> _savePreferences() async {
    if (_formKey.currentState!.validate()) {
      try {
        final user = FirebaseAuth.instance.currentUser;
        if (user != null) {
          await FirebaseFirestore.instance
              .collection('users')
              .doc(user.uid)
              .collection('academic_info')
              .doc('roadmap_preferences')
              .set({
            'target_graduation_year': _targetGraduationYear,
            'max_subjects_per_semester': _maxSubjectsPerSemester,
            'include_summer_semester': _includeSummerSemester,
            'updated_at': FieldValue.serverTimestamp(),
          });

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Preferences saved successfully!')),
            );
            Navigator.pop(context);
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error saving preferences: $e')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Roadmap Customization'),
        backgroundColor: const Color(0xFFA5D7C2),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              DropdownButtonFormField<String>(
                value: _random_department,
                decoration: const InputDecoration(
                  labelText: 'Select Your Department',
                  border: OutlineInputBorder(),
                ),
                items: [],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _random_department = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 20),

              DropdownButtonFormField<int>(
                value: _targetGraduationYear,
                decoration: const InputDecoration(
                  labelText: 'Target Graduation Year',
                  border: OutlineInputBorder(),
                ),
                items: List.generate(6, (index) => DateTime.now().year + index)
                    .map((year) => DropdownMenuItem(
                          value: year,
                          child: Text(year.toString()),
                        ))
                    .toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _targetGraduationYear = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 20),

              DropdownButtonFormField<int>(
                value: _maxSubjectsPerSemester,
                decoration: const InputDecoration(
                  labelText: 'Maximum Subjects per Semester',
                  border: OutlineInputBorder(),
                ),
                items: List.generate(8, (index) => index + 1)
                    .map((count) => DropdownMenuItem(
                          value: count,
                          child: Text('$count subjects'),
                        ))
                    .toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _maxSubjectsPerSemester = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 20),
              SwitchListTile(
                title: const Text('Include Summer Semester'),
                value: _includeSummerSemester,
                onChanged: (value) {
                  setState(() {
                    _includeSummerSemester = value;
                  });
                },
              ),
              const Spacer(),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _savePreferences,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF2E7D32),
                    padding: const EdgeInsets.symmetric(vertical: 15),
                  ),
                  child: const Text(
                    'Save Preferences',
                    style: TextStyle(fontSize: 16),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}