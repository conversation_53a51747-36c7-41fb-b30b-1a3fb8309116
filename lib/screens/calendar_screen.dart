import 'package:flutter/material.dart';
import '../widgets/header/header_widget.dart';
import 'home/components/navigation_icons.dart';
import 'input_menu_page.dart';

class CalendarScreen extends StatefulWidget {
  final List<List<Map<String, dynamic>>> topSchedules;
  final List<String> days;
  final List<String> timeSlots;

  const CalendarScreen({
    required this.topSchedules,
    required this.days,
    required this.timeSlots,
    Key? key,
  }) : super(key: key);

  @override
  State<CalendarScreen> createState() => _CalendarScreenState();
}

class _CalendarScreenState extends State<CalendarScreen> {
  List<Map<String, dynamic>> _displayedCourses = [];
  final ScrollController _horizontalScrollController = ScrollController();
  final ScrollController _headerScrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _horizontalScrollController.addListener(_syncScroll);
    _displayedCourses = widget.topSchedules.isNotEmpty ? widget.topSchedules[0] : [];
  }

  void _syncScroll() {
    if (_horizontalScrollController.hasClients &&
        _headerScrollController.hasClients) {
      _headerScrollController.jumpTo(_horizontalScrollController.offset);
    }
  }

  @override
  void dispose() {
    _horizontalScrollController.removeListener(_syncScroll);
    _horizontalScrollController.dispose();
    _headerScrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final headerHeight = 100.0;
    final bottomNavHeight = 80.0;
    final padding = screenHeight * 0.02;
    final availableHeight =
        screenHeight - headerHeight - bottomNavHeight - (padding * 2);

    return Scaffold(
      backgroundColor: Colors.white,
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const InputMenuPage()),
          );
        },
        backgroundColor: const Color(0xFFA5D7C2),
        child: const Icon(Icons.add),
      ),
      body: Column(
        children: [
          const HeaderWidget(),
          Padding(
            padding: EdgeInsets.symmetric(vertical: padding),
            child: SizedBox(
              height: availableHeight,
              child: _buildCalendarGrid(availableHeight),
            ),
          ),
          Container(
            width: MediaQuery.of(context).size.width,
            height: bottomNavHeight,
            decoration: const BoxDecoration(
              color: Color(0xFFA5D7C2),
            ),
            child: const NavigationIcons(),
          ),
        ],
      ),
    );
  }

  Widget _buildCalendarGrid(double availableHeight) {
    final screenWidth = MediaQuery.of(context).size.width;
    final timeColumnWidth = 100.0;
    final availableWidth = screenWidth - timeColumnWidth;
    final dayColumnWidth = availableWidth / 2;

    final headerHeight = 50.0;
    final fullSlotHeight = (availableHeight - headerHeight) / widget.timeSlots.length;
    final halfCellHeight = fullSlotHeight / 2;

    final filteredCourses = _displayedCourses;

    return Column(
      children: [
        // Header Row
        SizedBox(
          height: headerHeight,
          child: Row(
            children: [
              Container(
                width: timeColumnWidth,
                padding: const EdgeInsets.symmetric(vertical: 8),
                decoration: BoxDecoration(
                  border: Border.all(color: const Color(0xFF104B63)),
                ),
                child: const Text(
                  'Time',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Color(0xFF104B63),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Expanded(
                child: NotificationListener<ScrollNotification>(
                  onNotification: (ScrollNotification notification) {
                    if (notification is ScrollUpdateNotification) {
                      _horizontalScrollController.jumpTo(notification.metrics.pixels);
                    }
                    return true;
                  },
                  child: SingleChildScrollView(
                    controller: _headerScrollController,
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: widget.days.map((day) {
                        return Container(
                          width: dayColumnWidth,
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          decoration: BoxDecoration(
                            border: Border.all(color: const Color(0xFF104B63)),
                          ),
                          child: Text(
                            day,
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                              color: Color(0xFF104B63),
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),

        // Time Slots + Grid
        Expanded(
          child: NotificationListener<ScrollNotification>(
            onNotification: (ScrollNotification notification) {
              if (notification is ScrollUpdateNotification) {
                _headerScrollController.jumpTo(notification.metrics.pixels);
              }
              return true;
            },
            child: SingleChildScrollView(
              controller: _horizontalScrollController,
              scrollDirection: Axis.horizontal,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Time column (with 2 half-cells per period)
                  Column(
                    children: List.generate(widget.timeSlots.length * 2, (index) {
                      final timeLabel = index % 2 == 0 ? widget.timeSlots[index ~/ 2] : '';
                      return Container(
                        width: timeColumnWidth,
                        height: halfCellHeight,
                        decoration: BoxDecoration(
                          border: Border.all(color: const Color(0xFF104B63)),
                        ),
                        child: Center(
                          child: Text(
                            timeLabel,
                            style: const TextStyle(
                              color: Color(0xFF104B63),
                              fontSize: 12,
                            ),
                          ),
                        ),
                      );
                    }),
                  ),

                  // Day columns
                  ...widget.days.map((day) {
                    final List<Widget> dayCells = [];
                    final Set<int> occupiedHalfSlots = {};

                    for (int halfSlotIndex = 0; halfSlotIndex < widget.timeSlots.length * 2; halfSlotIndex++) {
                      if (occupiedHalfSlots.contains(halfSlotIndex)) continue;

                      final matchingCourse = filteredCourses.firstWhere(
                            (course) {
                          final courseStart = (course['timeSlotIndex'] * 2);
                          return course['day'] == day && courseStart == halfSlotIndex;
                        },
                        orElse: () => {},
                      );

                      if (matchingCourse.isNotEmpty) {
                        final double duration = matchingCourse['duration'] ?? 1.0;
                        final int cellSpan = (duration * 2).round();

                        for (int i = 0; i < cellSpan; i++) {
                          occupiedHalfSlots.add(halfSlotIndex + i);
                        }

                        dayCells.add(Container(
                          width: dayColumnWidth,
                          height: halfCellHeight * cellSpan,
                          decoration: BoxDecoration(
                            border: Border.all(color: const Color(0xFF104B63)),
                            color: Colors.tealAccent.withAlpha(102),
                          ),
                          child: Center(
                            child: Text(
                              matchingCourse['name'],
                              textAlign: TextAlign.center,
                              style: const TextStyle(
                                color: Color(0xFF104B63),
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ));
                      } else {
                        dayCells.add(Container(
                          width: dayColumnWidth,
                          height: halfCellHeight,
                          decoration: BoxDecoration(
                            border: Border.all(color: const Color(0xFF104B63)),
                          ),
                        ));
                      }
                    }

                    return Column(children: dayCells);
                  }).toList(),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}