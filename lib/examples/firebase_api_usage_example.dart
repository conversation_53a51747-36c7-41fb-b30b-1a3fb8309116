// Example usage of the updated getAllSubjects method with department filtering

import '../utils/firebase_api.dart';
import 'package:get/get.dart';

class FirebaseApiUsageExample {
  
  // Example 1: Get all subjects (no filtering)
  static Future<void> getAllSubjectsExample() async {
    try {
      final firebaseApi = Get.put(FirebaseApi());
      final allSubjects = await firebaseApi.getAllSubjects();
      
      print('Total subjects found: ${allSubjects.length}');
      for (var subject in allSubjects) {
        print('${subject['code']} - ${subject['englishName']} (Dept: ${subject['departmentCode']})');
      }
    } catch (e) {
      print('Error: $e');
    }
  }
  
  // Example 2: Get subjects filtered by department code
  static Future<void> getSubjectsByDepartmentCodeExample() async {
    try {
      final firebaseApi = Get.put(FirebaseApi());
      
      // Get subjects for Computer Engineering (assuming department code is 'computer engineering')
      final ceSubjects = await firebaseApi.getAllSubjects(department: 'computer engineering');
      
      print('Computer Engineering subjects found: ${ceSubjects.length}');
      for (var subject in ceSubjects) {
        print('${subject['code']} - ${subject['englishName']} (Semester: ${subject['semester_number']})');
      }
    } catch (e) {
      print('Error: $e');
    }
  }
  
  // Example 3: Get subjects by department name (using convenience method)
  static Future<void> getSubjectsByDepartmentNameExample() async {
    try {
      final firebaseApi = Get.put(FirebaseApi());
      
      // Get subjects for Computer Engineering using department name
      final ceSubjects = await firebaseApi.getSubjectsByDepartmentName('Computer Engineering');
      
      print('Computer Engineering subjects found: ${ceSubjects.length}');
      for (var subject in ceSubjects) {
        print('${subject['code']} - ${subject['englishName']} (Semester: ${subject['semester_number']})');
      }
    } catch (e) {
      print('Error: $e');
    }
  }
  
  // Example 4: Get subjects for multiple departments
  static Future<void> getSubjectsForMultipleDepartmentsExample() async {
    try {
      final firebaseApi = Get.put(FirebaseApi());
      
      final departments = ['Computer Engineering', 'Electrical Engineering', 'Mechanical Engineering'];
      
      for (String department in departments) {
        final subjects = await firebaseApi.getSubjectsByDepartmentName(department);
        print('\n$department subjects: ${subjects.length}');
        
        // Group by semester
        Map<int, List<Map<String, dynamic>>> subjectsBySemester = {};
        for (var subject in subjects) {
          final semester = subject['semester_number'] as int;
          if (!subjectsBySemester.containsKey(semester)) {
            subjectsBySemester[semester] = [];
          }
          subjectsBySemester[semester]!.add(subject);
        }
        
        // Print subjects grouped by semester
        for (int semester in subjectsBySemester.keys..sort()) {
          print('  Semester $semester: ${subjectsBySemester[semester]!.length} subjects');
          for (var subject in subjectsBySemester[semester]!) {
            print('    ${subject['code']} - ${subject['englishName']}');
          }
        }
      }
    } catch (e) {
      print('Error: $e');
    }
  }
}
