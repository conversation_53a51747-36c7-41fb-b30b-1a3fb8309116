import 'package:cloud_firestore/cloud_firestore.dart';

class Department {
  final String code;
  final String arabicName;
  final String englishName;
  final String departmentType;
  final String collegeType;
  final int total_hours;
  Department({
    required this.total_hours,
    required this.code,
    required this.arabicName,
    required this.englishName,
    required this.departmentType,
    required this.collegeType,
  });

  toJosn(){
    return {
      'code': code,
      'arabicName': arabicName,
      'englishName': englishName,
      'departmentType': departmentType,
      'collegeType': collegeType,
      'total_hours': total_hours,
    };
  }

  factory Department.fromSnapShot(DocumentSnapshot snap) {
    var snapshot = snap.data() as Map<String, dynamic>;
    return Department(
      code: snapshot['code'],
      arabicName: snapshot['arabicName'],
      englishName: snapshot['englishName'],
      departmentType: snapshot['departmentType'],
      collegeType: snapshot['collegeType'],
      total_hours: snapshot['total_hours'],
    );
  }

  @override
  String toString() {
    return 'Department('
    ' code: $code,\n'
    ' englishName: $englishName,\n'
    ' arabicName: $arabicName\n'
    ' total_hours $total_hours\n'
    ')\n';
  }
}
