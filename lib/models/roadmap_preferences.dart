class RoadmapPreferences {
  final int targetGraduationYear;
  final int maxSubjectsPerSemester;
  final bool includeSummerSemester;
  final String? selectedDepartment;

  const RoadmapPreferences({
    required this.targetGraduationYear,
    required this.maxSubjectsPerSemester,
    required this.includeSummerSemester,
    this.selectedDepartment,
  });

  // Calculate total semesters based on preferences
  int get totalSemesters {
    final currentYear = DateTime.now().year;
    final yearsToGraduation = targetGraduationYear - currentYear;
    final regularSemesters = yearsToGraduation * 2;
    
    // Add summer semesters if included
    final summerSemesters = includeSummerSemester ? yearsToGraduation : 0;
    
    return regularSemesters + summerSemesters;
  }

  // Convert to Map for easy serialization
  Map<String, dynamic> toMap() {
    return {
      'target_graduation_year': targetGraduationYear,
      'max_subjects_per_semester': maxSubjectsPerSemester,
      'include_summer_semester': includeSummerSemester,
      'selected_department': selectedDepartment,
    };
  }

  // Create from Map for easy deserialization
  factory RoadmapPreferences.fromMap(Map<String, dynamic> map) {
    return RoadmapPreferences(
      targetGraduationYear: map['target_graduation_year'] ?? DateTime.now().year + 4,
      maxSubjectsPerSemester: map['max_subjects_per_semester'] ?? 6,
      includeSummerSemester: map['include_summer_semester'] ?? true,
      selectedDepartment: map['selected_department'],
    );
  }

  // Create a copy with updated values
  RoadmapPreferences copyWith({
    int? targetGraduationYear,
    int? maxSubjectsPerSemester,
    bool? includeSummerSemester,
    String? selectedDepartment,
  }) {
    return RoadmapPreferences(
      targetGraduationYear: targetGraduationYear ?? this.targetGraduationYear,
      maxSubjectsPerSemester: maxSubjectsPerSemester ?? this.maxSubjectsPerSemester,
      includeSummerSemester: includeSummerSemester ?? this.includeSummerSemester,
      selectedDepartment: selectedDepartment ?? this.selectedDepartment,
    );
  }

  @override
  String toString() {
    return 'RoadmapPreferences(targetGraduationYear: $targetGraduationYear, maxSubjectsPerSemester: $maxSubjectsPerSemester, includeSummerSemester: $includeSummerSemester, selectedDepartment: $selectedDepartment)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is RoadmapPreferences &&
      other.targetGraduationYear == targetGraduationYear &&
      other.maxSubjectsPerSemester == maxSubjectsPerSemester &&
      other.includeSummerSemester == includeSummerSemester &&
      other.selectedDepartment == selectedDepartment;
  }

  @override
  int get hashCode {
    return targetGraduationYear.hashCode ^
      maxSubjectsPerSemester.hashCode ^
      includeSummerSemester.hashCode ^
      selectedDepartment.hashCode;
  }
}
