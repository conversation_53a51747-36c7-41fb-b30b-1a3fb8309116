// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBmkp9VWpGJQqoCy4lFi9UDQ_Pz0EQslyE',
    appId: '1:45402612933:web:ef2dde8a482576fd3ffcac',
    messagingSenderId: '45402612933',
    projectId: 'gradproject-f7884',
    authDomain: 'gradproject-f7884.firebaseapp.com',
    storageBucket: 'gradproject-f7884.firebasestorage.app',
    measurementId: 'G-G7N3H1RTZK',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAncoLnudDwOikvFFJu5HWLo-YAJFin5hM',
    appId: '1:45402612933:android:e74d3685c84cc1ae3ffcac',
    messagingSenderId: '45402612933',
    projectId: 'gradproject-f7884',
    storageBucket: 'gradproject-f7884.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyA0KG_hf8pf-CoUqsoXoMp1eT_wnKUZDL0',
    appId: '1:45402612933:ios:88d5eff05dbc6e153ffcac',
    messagingSenderId: '45402612933',
    projectId: 'gradproject-f7884',
    storageBucket: 'gradproject-f7884.firebasestorage.app',
    iosBundleId: 'com.example.finalgradgrad',
  );

}