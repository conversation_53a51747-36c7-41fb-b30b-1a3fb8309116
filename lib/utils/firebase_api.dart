
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import '../models/Subject.dart';

class FirebaseApi extends GetxController{
  static FirebaseApi get instance  => Get.find();

  // Future<List<Subject>> getAllSubjects () async {
  //   final snapshot = await FirebaseFirestore.instance.collection("subjects").get();
  //   final subject = snapshot.docs.map((e) => Subject.fromSnapShot(e)).toList();
  //   return subject;
  // }

  Future<List<Map<String, dynamic>>> getAllSubjects({String? department}) async {
    QuerySnapshot snapshot;

    // Use Firestore query to filter by department if provided (more efficient)
    if (department != null && department.isNotEmpty) {
      final departmentCodeToMatch = department.toLowerCase();
      snapshot = await FirebaseFirestore.instance
          .collection('subjects')
          .where('departmentCode', isEqualTo: departmentCodeToMatch)
          .get();
    } else {
      // Get all subjects if no department filter
      snapshot = await FirebaseFirestore.instance
          .collection('subjects')
          .get();
    }

    final List<Map<String, dynamic>> subjects = snapshot.docs.map((doc) {
      final data = doc.data() as Map<String, dynamic>;
      print('Processing document: ${doc.id}');
      return {
        'id': doc.id,
        'code': data['code'] ?? '',
        'englishName': data['englishName'] ?? '',
        'arabicName': data['arabicName'] ?? '',
        'semester_number': data['semester_number'] ?? 1,
        'departmentCode': data['departmentCode'] ?? '',
        'hours': data['hours'] ?? 0,
        'type': data['type'] ?? '',
        'prerequisites': List<String>.from(data['prerequisites'] ?? []),
        'dependencies': List<String>.from(data['dependencies'] ?? []),
        'status': data['status'] ?? false,
      };
    }).toList();

    return subjects;
  }

  Future<List<Map<String, dynamic>>> getAllDepartments () async {
    final QuerySnapshot snapshot = await FirebaseFirestore.instance
        .collection('departments')
        .get();

    final List<Map<String, dynamic>> departments = snapshot.docs.map((doc) {
      final data = doc.data() as Map<String, dynamic>;
      print('Processing document: ${doc.id}');
      return {
        'id': doc.id,
        'minimum graduation_hours': data['graduation_hours'] ?? 180,
        'name': data['name'] ?? '',
      };
    }).toList();

    return departments;
  }


  Future<List<Map<String, dynamic>>> getAllDoctors () async {
    final QuerySnapshot snapshot = await FirebaseFirestore.instance
        .collection('doctors')
        .get();

    final List<Map<String, dynamic>> doctors = snapshot.docs.map((doc) {
      final data = doc.data() as Map<String, dynamic>;
      print('Processing doctor document: ${doc.id}');
      return {
        'id': doc.id,
        'name': data['name'] ?? '',
        'department': data['department'] ?? '',
        'email': data['email'] ?? '',
        'office': data['office'] ?? '',
        'imageUrl': data['imageUrl'] ?? 'https://via.placeholder.com/150',
      };
    }).toList();

    return doctors;
  }

  // Helper method to get department code from department name
  Future<String?> getDepartmentCode(String departmentName) async {
    try {
      final departments = await getAllDepartments();
      final department = departments.firstWhere(
        (dept) => (dept['name'] as String).toLowerCase() == departmentName.toLowerCase(),
        orElse: () => {},
      );

      if (department.isNotEmpty) {
        // Return the department name in lowercase as the code
        return (department['name'] as String).toLowerCase();
      }
      return null;
    } catch (e) {
      print('Error getting department code: $e');
      return null;
    }
  }

  // Convenience method to get subjects by department name (instead of code)
  Future<List<Map<String, dynamic>>> getSubjectsByDepartmentName(String departmentName) async {
    final departmentCode = await getDepartmentCode(departmentName);
    if (departmentCode != null) {
      return getAllSubjects(department: departmentCode);
    }
    return [];
  }

}
