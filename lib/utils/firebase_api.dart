
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import '../models/Subject.dart';

class FirebaseApi extends GetxController{
  static FirebaseApi get instance  => Get.find();

  // Future<List<Subject>> getAllSubjects () async {
  //   final snapshot = await FirebaseFirestore.instance.collection("subjects").get();
  //   final subject = snapshot.docs.map((e) => Subject.fromSnapShot(e)).toList();
  //   return subject;
  // }

  Future<List<Map<String, dynamic>>> getAllSubjects () async {
    final QuerySnapshot snapshot = await FirebaseFirestore.instance
        .collection('subjects')
        .get();

    final List<Map<String, dynamic>> subjects = snapshot.docs.map((doc) {
      final data = doc.data() as Map<String, dynamic>;
      print('Processing document: ${doc.id}');
      return {
        'id': doc.id,
        'code': data['code'] ?? '',
        'englishName': data['englishName'] ?? '',
        'arabicName': data['arabicName'] ?? '',
        'semester_number': data['semester_number'] ?? 1,
        'departmentCode': data['departmentCode'] ?? '',
        'hours': data['hours'] ?? 0,
        'type': data['type'] ?? '',
        'prerequisites': List<String>.from(data['prerequisites'] ?? []),
        'dependencies': List<String>.from(data['dependencies'] ?? []),
        'status': data['status'] ?? false,
      };
    }).toList();

    return subjects;
  }

  Future<List<Map<String, dynamic>>> getAllDepartments () async {
    final QuerySnapshot snapshot = await FirebaseFirestore.instance
        .collection('departments')
        .get();

    final List<Map<String, dynamic>> departments = snapshot.docs.map((doc) {
      final data = doc.data() as Map<String, dynamic>;
      print('Processing document: ${doc.id}');
      return {
        'id': doc.id,
        'minimum graduation_hours': data['graduation_hours'] ?? 180,
        'name': data['name'] ?? '',
      };
    }).toList();

    return departments;
  }


  Future<List<Map<String, dynamic>>> getAllDoctors () async {
    final QuerySnapshot snapshot = await FirebaseFirestore.instance
        .collection('doctors')
        .get();

    final List<Map<String, dynamic>> departments = snapshot.docs.map((doc) {
      final data = doc.data() as Map<String, dynamic>;
      return {
        'id': doc.id,
        'department': data['department'] ?? 180,
        'email.com': data['email.com'] ?? '',
        '`name`': data['name'] ?? '',
      };
    }).toList();

    return departments;
  }  

}
